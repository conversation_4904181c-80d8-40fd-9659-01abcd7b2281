from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from pydantic import BaseModel, ConfigDict, Field, field_validator

from app.schemas.course import Course
from app.schemas.user import User


# Tutor Profile Schemas
class TutorProfileBase(BaseModel):
    bio: Optional[str] = None
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    is_available: bool = True
    preferred_session_type: Optional[str] = Field(None, pattern="^(online|in_person|both)$")
    max_students_per_session: int = Field(1, ge=1, le=20)
    linkedin_url: Optional[str] = None
    website_url: Optional[str] = None
    location: Optional[str] = None
    gender: Optional[str] = Field(None, pattern="^(male|female|other|prefer_not_to_say)$")
    languages: Optional[List[str]] = None


class TutorProfileCreate(TutorProfileBase):
    specialization_ids: List[int] = []  # Course IDs the tutor can teach


class TutorProfileUpdate(BaseModel):
    bio: Optional[str] = None
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    is_available: Optional[bool] = None
    preferred_session_type: Optional[str] = Field(None, pattern="^(online|in_person|both)$")
    max_students_per_session: Optional[int] = Field(None, ge=1, le=20)
    linkedin_url: Optional[str] = None
    website_url: Optional[str] = None
    location: Optional[str] = None
    gender: Optional[str] = Field(None, pattern="^(male|female|other|prefer_not_to_say)$")
    languages: Optional[List[str]] = None
    specialization_ids: Optional[List[int]] = None


class TutorProfileInDBBase(TutorProfileBase):
    id: int
    user_id: int
    average_rating: Optional[Decimal] = None
    total_reviews: int = 0
    total_sessions_completed: int = 0
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TutorProfile(TutorProfileInDBBase):
    user: Optional[User] = None
    specializations: Optional[List[Course]] = None

    @field_validator('languages', mode='before')
    @classmethod
    def parse_languages(cls, v):
        """Convert comma-separated string to list"""
        if isinstance(v, str) and v:
            return v.split(',')
        elif isinstance(v, list):
            return v
        return []


class TutorProfilePublic(TutorProfileInDBBase):
    """Public profile for students to view tutors"""
    user: Optional[User] = None
    specializations: Optional[List[Course]] = None


# Tutor Review Schemas
class TutorReviewBase(BaseModel):
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = None


class TutorReviewCreate(TutorReviewBase):
    tutor_profile_id: int
    session_id: Optional[int] = None


class TutorReviewUpdate(BaseModel):
    rating: Optional[int] = Field(None, ge=1, le=5)
    comment: Optional[str] = None


class TutorReviewInDBBase(TutorReviewBase):
    id: int
    tutor_profile_id: int
    student_id: int
    session_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TutorReview(TutorReviewInDBBase):
    student: Optional[User] = None


# Dashboard and Analytics Schemas
class TutorDashboardStats(BaseModel):
    total_sessions: int
    completed_sessions: int
    upcoming_sessions: int
    total_students: int
    average_rating: Optional[Decimal] = None
    total_reviews: int
    total_earnings: Optional[Decimal] = None
    this_month_sessions: int
    this_month_earnings: Optional[Decimal] = None


class TutorSessionAnalytics(BaseModel):
    session_id: int
    session_title: str
    student_name: Optional[str] = None
    course_name: str
    start_time: datetime
    end_time: datetime
    status: str
    earnings: Optional[Decimal] = None


# Tutor Discovery Schemas
class TutorSearchFilters(BaseModel):
    course_ids: Optional[List[int]] = None
    min_rating: Optional[float] = Field(None, ge=0, le=5)
    max_hourly_rate: Optional[Decimal] = Field(None, ge=0)
    session_type: Optional[str] = Field(None, pattern="^(online|in_person|both)$")
    is_available: bool = True
    experience_years_min: Optional[int] = Field(None, ge=0)


class TutorListItem(BaseModel):
    """Simplified tutor info for listing pages"""
    id: int
    user_id: int
    full_name: str
    bio: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[Decimal] = None
    average_rating: Optional[Decimal] = None
    total_reviews: int = 0
    total_sessions_completed: int = 0
    is_available: bool = True
    preferred_session_type: Optional[str] = None
    location: Optional[str] = None
    gender: Optional[str] = None
    languages: List[str] = []  # Languages spoken
    specializations: List[str] = []  # Course names
    profile_picture_url: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('languages', mode='before')
    @classmethod
    def parse_languages(cls, v):
        """Convert comma-separated string to list"""
        if isinstance(v, str) and v:
            return v.split(',')
        elif isinstance(v, list):
            return v
        return []
