from datetime import datetime
from typing import Dict, Optional, Union

from pydantic import BaseModel, ConfigDict

from app.models.question import Difficulty<PERSON><PERSON>l, QuestionType


# Shared properties
class QuestionBase(BaseModel):
    content: str
    question_type: QuestionType
    difficulty: DifficultyLevel
    topic: Optional[str] = None  # Topic the question belongs to
    options: Optional[Dict[str, str]] = None  # For multiple choice
    answer: str
    explanation: Optional[str] = None
    media_url: Optional[str] = None
    is_active: bool = True
    course_id: Optional[int] = None  # Optional for note-generated questions
    course_name: Optional[str] = None  # Course name from PDF upload
    mcq_job_id: Optional[int] = None  # For note-generated questions


# Properties to receive via API on creation
class QuestionCreate(QuestionBase):
    created_by_id: Optional[int] = None


# Properties to receive via API on update
class QuestionUpdate(BaseModel):
    content: Optional[str] = None
    question_type: Optional[QuestionType] = None
    difficulty: Optional[DifficultyLevel] = None
    topic: Optional[str] = None
    options: Optional[Dict[str, str]] = None
    answer: Optional[str] = None
    explanation: Optional[str] = None
    media_url: Optional[str] = None
    is_active: Optional[bool] = None
    course_id: Optional[int] = None
    mcq_job_id: Optional[int] = None


# Properties shared by models stored in DB
class QuestionInDBBase(QuestionBase):
    id: int
    created_by_id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Question(QuestionInDBBase):
    pass


# AI-generated question request
class AIQuestionRequest(BaseModel):
    course_id: int
    question_type: QuestionType
    difficulty: DifficultyLevel
    topic: str
    num_questions: int = 1
