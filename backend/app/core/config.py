import os
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, PostgresDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True)

    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "CampusPQ"
    DEBUG: bool = False
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database
    DATABASE_URL: str

    # OpenAI
    OPENAI_API_KEY: str

    # Gemini
    GEMINI_API_KEY: str

    # Redis (for Celery)
    REDIS_URL: str = "redis://localhost:6379/0"

    # Qdrant
    QDRANT_URL: str = "http://localhost:6333"
    QDRANT_COLLECTION: str = "campuspq_embeddings"

    # Enhanced PDF Processing Settings
    OCR_CONFIDENCE_THRESHOLD: float = 60.0
    OCR_LOW_CONFIDENCE_THRESHOLD: float = 40.0
    DEFAULT_CHUNK_SIZE: int = 1200
    CHUNK_OVERLAP: int = 200
    ENABLE_HANDWRITING_OCR: bool = True
    ENABLE_ENHANCED_PIPELINE: bool = True

    # Caching Settings
    CACHE_ENABLED: bool = True
    CACHE_TTL_HOURS: int = 24

    # Performance Settings
    MAX_PARALLEL_CHUNKS: int = 5
    BATCH_SIZE: int = 3

    # Email Settings
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: str = "CampusPQ"
    EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS: int = 24
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = 1

    # Frontend URL for email verification links
    FRONTEND_URL: str = "http://localhost:5173"


settings = Settings()
