from typing import List, Optional
from decimal import Decimal
from datetime import datetime, timedelta

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_

from app.models.tutor import TutorProfile, TutorReview
from app.models.user import User, UserRole
from app.models.course import Course
from app.models.session import Session as TutorSession, SessionStatus
from app.schemas import tutor as tutor_schemas
from app.crud.base import CRUDBase


class TutorProfileService(CRUDBase[TutorProfile, tutor_schemas.TutorProfileCreate, tutor_schemas.TutorProfileUpdate]):
    def __init__(self):
        super().__init__(TutorProfile)

    def get_by_user_id(self, db: Session, user_id: int) -> Optional[TutorProfile]:
        """Get tutor profile by user ID"""
        return db.query(TutorProfile).filter(TutorProfile.user_id == user_id).first()

    def create_for_user(self, db: Session, *, obj_in: tutor_schemas.TutorProfileCreate, user_id: int) -> TutorProfile:
        """Create tutor profile for a specific user"""
        # Create the profile
        db_obj = TutorProfile(
            user_id=user_id,
            bio=obj_in.bio,
            experience_years=obj_in.experience_years,
            hourly_rate=obj_in.hourly_rate,
            is_available=obj_in.is_available,
            preferred_session_type=obj_in.preferred_session_type,
            max_students_per_session=obj_in.max_students_per_session,
            linkedin_url=obj_in.linkedin_url,
            website_url=obj_in.website_url,
        )
        db.add(db_obj)
        db.flush()  # Get the ID

        # Add specializations
        if obj_in.specialization_ids:
            courses = db.query(Course).filter(Course.id.in_(obj_in.specialization_ids)).all()
            db_obj.specializations = courses

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_with_specializations(
        self, db: Session, *, db_obj: TutorProfile, obj_in: tutor_schemas.TutorProfileUpdate
    ) -> TutorProfile:
        """Update tutor profile including specializations"""
        # Update basic fields
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"specialization_ids"})
        for field, value in update_data.items():
            setattr(db_obj, field, value)

        # Update specializations if provided
        if obj_in.specialization_ids is not None:
            courses = db.query(Course).filter(Course.id.in_(obj_in.specialization_ids)).all()
            db_obj.specializations = courses

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_with_details(self, db: Session, id: int) -> Optional[TutorProfile]:
        """Get tutor profile with user and specializations"""
        return (
            db.query(TutorProfile)
            .options(
                joinedload(TutorProfile.user),
                joinedload(TutorProfile.specializations)
            )
            .filter(TutorProfile.id == id)
            .first()
        )

    def search_tutors(
        self, 
        db: Session, 
        *, 
        filters: tutor_schemas.TutorSearchFilters,
        skip: int = 0,
        limit: int = 100
    ) -> List[TutorProfile]:
        """Search tutors with filters"""
        query = (
            db.query(TutorProfile)
            .join(User)
            .options(
                joinedload(TutorProfile.user),
                joinedload(TutorProfile.specializations)
            )
            .filter(User.role == UserRole.TUTOR)
            .filter(User.is_active == True)
        )

        # Apply filters
        if filters.is_available:
            query = query.filter(TutorProfile.is_available == True)

        if filters.min_rating is not None:
            query = query.filter(TutorProfile.average_rating >= filters.min_rating)

        if filters.max_hourly_rate is not None:
            query = query.filter(TutorProfile.hourly_rate <= filters.max_hourly_rate)

        if filters.session_type:
            if filters.session_type in ['online', 'in_person']:
                query = query.filter(
                    or_(
                        TutorProfile.preferred_session_type == filters.session_type,
                        TutorProfile.preferred_session_type == 'both'
                    )
                )

        if filters.experience_years_min is not None:
            query = query.filter(TutorProfile.experience_years >= filters.experience_years_min)

        if filters.course_ids:
            query = query.join(TutorProfile.specializations).filter(
                Course.id.in_(filters.course_ids)
            )

        return query.offset(skip).limit(limit).all()

    def get_dashboard_stats(self, db: Session, tutor_profile_id: int) -> tutor_schemas.TutorDashboardStats:
        """Get dashboard statistics for a tutor"""
        # Get basic session counts
        total_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .scalar()
        )

        completed_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.status == SessionStatus.COMPLETED
                )
            )
            .scalar()
        )

        upcoming_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.status == SessionStatus.SCHEDULED,
                    TutorSession.start_time > datetime.utcnow()
                )
            )
            .scalar()
        )

        # Get unique students count
        total_students = (
            db.query(func.count(func.distinct(TutorSession.student_id)))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.student_id.isnot(None)
                )
            )
            .scalar()
        )

        # Get tutor profile for rating info
        tutor_profile = db.query(TutorProfile).filter(TutorProfile.id == tutor_profile_id).first()

        # Calculate this month's sessions
        start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.created_at >= start_of_month
                )
            )
            .scalar()
        )

        return tutor_schemas.TutorDashboardStats(
            total_sessions=total_sessions or 0,
            completed_sessions=completed_sessions or 0,
            upcoming_sessions=upcoming_sessions or 0,
            total_students=total_students or 0,
            average_rating=tutor_profile.average_rating if tutor_profile else None,
            total_reviews=tutor_profile.total_reviews if tutor_profile else 0,
            total_earnings=None,  # To be implemented with payment system
            this_month_sessions=this_month_sessions or 0,
            this_month_earnings=None,  # To be implemented with payment system
        )


class TutorReviewService(CRUDBase[TutorReview, tutor_schemas.TutorReviewCreate, tutor_schemas.TutorReviewUpdate]):
    def __init__(self):
        super().__init__(TutorReview)

    def create_review(
        self, 
        db: Session, 
        *, 
        obj_in: tutor_schemas.TutorReviewCreate, 
        student_id: int
    ) -> TutorReview:
        """Create a review and update tutor's average rating"""
        # Create the review
        db_obj = TutorReview(
            tutor_profile_id=obj_in.tutor_profile_id,
            student_id=student_id,
            session_id=obj_in.session_id,
            rating=obj_in.rating,
            comment=obj_in.comment,
        )
        db.add(db_obj)
        db.flush()

        # Update tutor's average rating
        self._update_tutor_rating(db, obj_in.tutor_profile_id)

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def _update_tutor_rating(self, db: Session, tutor_profile_id: int):
        """Update tutor's average rating and review count"""
        # Calculate new average rating
        result = (
            db.query(
                func.avg(TutorReview.rating).label('avg_rating'),
                func.count(TutorReview.id).label('total_reviews')
            )
            .filter(TutorReview.tutor_profile_id == tutor_profile_id)
            .first()
        )

        # Update tutor profile
        tutor_profile = db.query(TutorProfile).filter(TutorProfile.id == tutor_profile_id).first()
        if tutor_profile:
            tutor_profile.average_rating = result.avg_rating
            tutor_profile.total_reviews = result.total_reviews

    def get_reviews_for_tutor(
        self, 
        db: Session, 
        tutor_profile_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[TutorReview]:
        """Get reviews for a specific tutor"""
        return (
            db.query(TutorReview)
            .options(joinedload(TutorReview.student))
            .filter(TutorReview.tutor_profile_id == tutor_profile_id)
            .order_by(TutorReview.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


# Service instances
tutor_profile_service = TutorProfileService()
tutor_review_service = TutorReviewService()
