from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User, UserRole
from app.models.question import DifficultyLevel, QuestionType
from app.api import deps
from app.services import question_service

router = APIRouter()


@router.get("/", response_model=List[schemas.Question])
def read_questions(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    course_id: int = None,
    difficulty: DifficultyLevel = None,
    question_type: QuestionType = None,
    topic: str = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve questions with optional filtering.
    """
    questions = question_service.get_multi(
        db,
        skip=skip,
        limit=limit,
        course_id=course_id,
        difficulty=difficulty,
        question_type=question_type,
        topic=topic
    )
    return questions


@router.post("/", response_model=schemas.Question)
def create_question(
    *,
    db: Session = Depends(deps.get_db),
    question_in: schemas.QuestionCreate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Create new question.
    """
    question = question_service.create(
        db,
        obj_in=question_in,
        created_by_id=current_user.id
    )
    return question


@router.get("/{id}", response_model=schemas.Question)
def read_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get question by ID.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return question


@router.put("/{id}", response_model=schemas.Question)
def update_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    question_in: schemas.QuestionUpdate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Update a question.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Only the creator or an admin can update the question
    if question.created_by_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    question = question_service.update(db, db_obj=question, obj_in=question_in)
    return question


@router.delete("/{id}", response_model=schemas.Question)
def delete_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Delete a question.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Only the creator or an admin can delete the question
    if question.created_by_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    question = question_service.delete(db, id=id)
    return question


@router.post("/upload-pdf", response_model=List[schemas.Question])
async def upload_pdf_for_questions(
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    course_id: int,
    question_type: QuestionType,
    difficulty: DifficultyLevel,
    num_questions: int = 5,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Upload a PDF and generate questions from it.
    """
    # This is a placeholder. In a real implementation, you would:
    # 1. Save the uploaded PDF
    # 2. Process it with a PDF parser
    # 3. Use AI to generate questions from the content
    # 4. Save the questions to the database

    # For now, we'll just return a placeholder response
    raise HTTPException(
        status_code=501,
        detail="PDF upload and processing not implemented yet"
    )
