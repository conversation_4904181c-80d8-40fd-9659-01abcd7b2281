import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import TutorLayout from '../components/Layout/TutorLayout';
import TutorDashboard from '../pages/tutor/TutorDashboard';
import TutorProfileForm from '../pages/tutor/TutorProfileForm';
import TutorStudents from '../pages/tutor/TutorStudents';
import TutorEarnings from '../pages/tutor/TutorEarnings';
import TutorReviews from '../pages/tutor/TutorReviews';
import TutorAnalytics from '../pages/tutor/TutorAnalytics';

// Import existing session pages that tutors can access
import SessionsList from '../pages/sessions/SessionsList';
import SessionDetail from '../pages/sessions/SessionDetail';
import SessionForm from '../pages/sessions/SessionForm';

const TutorRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Tutor Protected Routes with Layout */}
      <Route element={<ProtectedRoute requiredRole="tutor" />}>
        <Route element={<TutorLayout />}>
          {/* Dashboard */}
          <Route path="/" element={<Navigate to="/tutor/dashboard" replace />} />
          <Route path="/dashboard" element={<TutorDashboard />} />
          
          {/* Profile Management */}
          <Route path="/profile" element={<TutorProfileForm />} />

          {/* Session Management */}
          <Route path="/sessions" element={<SessionsList />} />
          <Route path="/sessions/new" element={<SessionForm />} />
          <Route path="/sessions/:id" element={<SessionDetail />} />
          <Route path="/sessions/:id/edit" element={<SessionForm />} />

          {/* Tutor-specific pages */}
          <Route path="/students" element={<TutorStudents />} />
          <Route path="/earnings" element={<TutorEarnings />} />
          <Route path="/reviews" element={<TutorReviews />} />
          <Route path="/analytics" element={<TutorAnalytics />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default TutorRoutes;
