import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import AdminLayout from '../components/Layout/AdminLayout';
import AdminLogin from '../pages/admin/AdminLogin';
import AdminDashboard from '../pages/admin/AdminDashboard';
import BulkUserManagement from '../pages/admin/BulkUserManagement';
import AdminAnalytics from '../pages/admin/AdminAnalytics';

// Import existing admin pages
import UsersList from '../pages/users/UsersList';
import UserDetail from '../pages/users/UserDetail';
import UserForm from '../pages/users/UserForm';

import SchoolsList from '../pages/schools/SchoolsList';
import SchoolDetail from '../pages/schools/SchoolDetail';
import SchoolForm from '../pages/schools/SchoolForm';

import DepartmentsList from '../pages/departments/DepartmentsList';
import DepartmentDetail from '../pages/departments/DepartmentDetail';
import DepartmentForm from '../pages/departments/DepartmentForm';

import CoursesList from '../pages/courses/CoursesList';
import CourseDetail from '../pages/courses/CourseDetail';
import CourseForm from '../pages/courses/CourseForm';

import QuestionsList from '../pages/questions/QuestionsList';
import QuestionDetail from '../pages/questions/QuestionDetail';
import QuestionForm from '../pages/questions/QuestionForm';

const AdminRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Admin Login - No layout */}
      <Route path="/login" element={<AdminLogin />} />

      {/* Admin Protected Routes with Layout */}
      <Route element={<ProtectedRoute requiredRole="admin" />}>
        <Route element={<AdminLayout />}>
          {/* Dashboard */}
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="/dashboard" element={<AdminDashboard />} />
          
          {/* Users Management */}
          <Route path="/users" element={<UsersList />} />
          <Route path="/users/new" element={<UserForm />} />
          <Route path="/users/:id" element={<UserDetail />} />
          <Route path="/users/:id/edit" element={<UserForm />} />
          <Route path="/users/bulk" element={<BulkUserManagement />} />

          {/* Schools Management */}
          <Route path="/schools" element={<SchoolsList />} />
          <Route path="/schools/new" element={<SchoolForm />} />
          <Route path="/schools/:id" element={<SchoolDetail />} />
          <Route path="/schools/:id/edit" element={<SchoolForm />} />

          {/* Departments Management */}
          <Route path="/departments" element={<DepartmentsList />} />
          <Route path="/departments/new" element={<DepartmentForm />} />
          <Route path="/departments/:id" element={<DepartmentDetail />} />
          <Route path="/departments/:id/edit" element={<DepartmentForm />} />

          {/* Courses Management */}
          <Route path="/courses" element={<CoursesList />} />
          <Route path="/courses/new" element={<CourseForm />} />
          <Route path="/courses/:id" element={<CourseDetail />} />
          <Route path="/courses/:id/edit" element={<CourseForm />} />

          {/* Questions Management */}
          <Route path="/questions" element={<QuestionsList />} />
          <Route path="/questions/new" element={<QuestionForm />} />
          <Route path="/questions/:id" element={<QuestionDetail />} />
          <Route path="/questions/:id/edit" element={<QuestionForm />} />

          {/* Analytics */}
          <Route path="/analytics" element={<AdminAnalytics />} />
          <Route
            path="/settings"
            element={
              <div style={{ padding: '20px' }}>
                <h2>System Settings</h2>
                <p>Coming soon - Platform configuration and settings</p>
              </div>
            }
          />
        </Route>
      </Route>
    </Routes>
  );
};

export default AdminRoutes;
