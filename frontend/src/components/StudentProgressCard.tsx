import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Grid,
  useTheme,
  useMediaQuery,
  Avatar
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  EmojiEvents as TrophyIcon,
  School as GraduationIcon,
  Quiz as QuizIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface StudentProgressCardProps {
  totalPoints?: number;
  level?: number;
  quizzesTaken?: number;
  questionsAnswered?: number;
  streak?: number;
}

const StudentProgressCard: React.FC<StudentProgressCardProps> = ({
  totalPoints = 0,
  level = 1,
  quizzesTaken = 0,
  questionsAnswered = 0,
  streak = 0
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Calculate progress to next level (example calculation)
  const pointsToNextLevel = level * 100;
  const progress = Math.min(100, (totalPoints % pointsToNextLevel) / pointsToNextLevel * 100);
  
  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  const MotionCard = motion(Card);

  return (
    <MotionCard
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: `1px solid ${theme.palette.divider}`,
        height: '100%'
      }}
    >
      <CardContent sx={{ p: isMobile ? 2 : 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: 40,
              height: 40,
              mr: 2
            }}
          >
            <TrophyIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Level {level}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {totalPoints} total points
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2" color="text.secondary">
              Progress to Level {level + 1}
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {Math.round(progress)}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{ 
              height: 8, 
              borderRadius: 4,
              bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }
            }} 
          />
        </Box>
        
        <Grid container spacing={isMobile ? 1 : 2}>
          <Grid item xs={6}>
            <Card 
              sx={{ 
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
                boxShadow: 'none',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1.5
              }}
            >
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <QuizIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    Quizzes
                  </Typography>
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {quizzesTaken}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6}>
            <Card 
              sx={{ 
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
                boxShadow: 'none',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1.5
              }}
            >
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <GraduationIcon fontSize="small" color="secondary" sx={{ mr: 0.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    Questions
                  </Typography>
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {questionsAnswered}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        {streak > 0 && (
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Chip 
              icon={<TrendingUpIcon />} 
              label={`${streak} Day Streak!`} 
              color="primary" 
              sx={{ borderRadius: 1 }}
            />
          </Box>
        )}
      </CardContent>
    </MotionCard>
  );
};

export default StudentProgressCard;
