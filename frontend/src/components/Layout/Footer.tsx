import React from 'react';
import { Box, Container, Typography, Link, Grid, IconButton, useTheme, Divider } from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

const Footer: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Create motion components
  const MotionBox = motion(Box);
  const MotionGrid = motion(Grid);

  return (
    <Box
      component="footer"
      sx={{
        py: 3,
        px: 2,
        backgroundColor: theme.palette.mode === 'light'
          ? 'rgba(245, 245, 245, 0.8)'
          : 'rgba(30, 30, 30, 0.8)',
        backdropFilter: 'blur(10px)',
        borderTop: `1px solid ${theme.palette.divider}`,
        position: 'relative',
        width: '100%',
        zIndex: 10,
      }}
    >
      <Container maxWidth="lg">
        <MotionGrid
          container
          spacing={4}
          component={motion.div}
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Logo and description */}
          <MotionGrid item xs={12} md={4} variants={itemVariants}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SchoolIcon sx={{ fontSize: 24, mr: 1, color: 'primary.main' }} />
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                CampusPQ
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" paragraph>
              An AI-driven learning ecosystem designed to connect students with tutors and provide personalized educational experiences.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <IconButton size="small" color="primary">
                <FacebookIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" color="primary">
                <TwitterIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" color="primary">
                <InstagramIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" color="primary">
                <LinkedInIcon fontSize="small" />
              </IconButton>
            </Box>
          </MotionGrid>

          {/* Quick links */}
          <MotionGrid item xs={12} sm={6} md={4} variants={itemVariants}>
            <Typography variant="subtitle1" color="text.primary" gutterBottom>
              Quick Links
            </Typography>
            <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/" color="text.secondary" underline="hover">
                  Home
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/courses" color="text.secondary" underline="hover">
                  Courses
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/sessions" color="text.secondary" underline="hover">
                  Sessions
                </Link>
              </Box>
              {(user?.role === 'tutor' || user?.role === 'admin') && (
                <Box component="li" sx={{ mb: 1 }}>
                  <Link component={RouterLink} to="/questions" color="text.secondary" underline="hover">
                    Questions
                  </Link>
                </Box>
              )}
            </Box>
          </MotionGrid>

          {/* Contact info */}
          <MotionGrid item xs={12} sm={6} md={4} variants={itemVariants}>
            <Typography variant="subtitle1" color="text.primary" gutterBottom>
              Contact Us
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Have questions or feedback? Reach out to us.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Email: <EMAIL>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Phone: +****************
            </Typography>
          </MotionGrid>
        </MotionGrid>

        <Divider sx={{ my: 3 }} />

        <MotionBox
          sx={{ textAlign: 'center' }}
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          viewport={{ once: true }}
        >
          <Typography variant="body2" color="text.secondary">
            {'© '}
            {new Date().getFullYear()}
            {' '}
            <Link color="inherit" component={RouterLink} to="/">
              CampusPQ
            </Link>
            {' - All rights reserved'}
          </Typography>
        </MotionBox>
      </Container>
    </Box>
  );
};

export default Footer;
