import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  useTheme,
  Paper,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Dashboard,
  School as SchoolIcon,
  Book,
  QuestionAnswer,
  Event,
  Person,
  Settings,
  AccountCircle,
  Quiz as QuizIcon,
  EmojiEvents as TrophyIcon,
  AutoAwesome as AutoAwesomeIcon,
  ViewCarousel as CardIcon,
  PersonSearch
} from '@mui/icons-material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';
import ThemeToggle from '../ThemeToggle';

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const location = useLocation();

  // Define menu items based on user role
  const menuItems = [
    { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
    { text: 'Courses', icon: <Book />, path: '/courses' },
    { text: 'Sessions', icon: <Event />, path: '/sessions' },
    { text: 'MCQ Dashboard', icon: <QuizIcon />, path: '/mcq' },
  ];

  // Add Questions menu item only for tutors and admins
  if (user?.role === 'tutor' || user?.role === 'admin') {
    menuItems.splice(2, 0, { text: 'Questions', icon: <QuestionAnswer />, path: '/questions' });
  }

  // Add student-specific menu items
  if (user?.role === 'student') {
    menuItems.push(
      { text: 'Find Tutors', icon: <PersonSearch />, path: '/tutors' },
      { text: 'Tools', icon: <AutoAwesomeIcon />, path: '/tools' },
      { text: 'Flash Cards', icon: <CardIcon />, path: '/flash-cards' },
      { text: 'Gamification', icon: <TrophyIcon />, path: '/gamification' }
    );
  }

  // Add admin-only menu items
  if (user?.role === 'admin') {
    menuItems.push(
      { text: 'Schools', icon: <SchoolIcon />, path: '/schools' },
      { text: 'Departments', icon: <Book />, path: '/departments' },
      { text: 'Users', icon: <Person />, path: '/users' }
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const MotionList = motion(List);
  const MotionListItem = motion(ListItem);

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        borderRadius: 2,
        border: `1px solid ${theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.05)'}`,
        overflow: 'hidden',
        position: 'sticky',
        top: 80,
      }}
    >
      {/* User profile section */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          borderBottom: `1px solid ${theme.palette.divider}`,
          mb: 2,
        }}
      >
        <Avatar
          sx={{
            width: 64,
            height: 64,
            mb: 1,
            bgcolor: theme.palette.primary.main,
            border: `2px solid ${theme.palette.background.paper}`,
            boxShadow: `0 0 0 2px ${theme.palette.primary.main}`,
          }}
        >
          {user?.full_name?.charAt(0) || <AccountCircle />}
        </Avatar>
        <Typography variant="subtitle1" fontWeight="bold" align="center">
          {user?.full_name}
        </Typography>
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 1 }}>
          {user?.role === 'student' ? 'Student' : user?.role === 'tutor' ? 'Tutor' : 'Administrator'}
        </Typography>

        <Box sx={{ mt: 1 }}>
          <ThemeToggle />
        </Box>
      </Box>

      {/* Navigation menu */}
      <MotionList
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        sx={{ px: 1 }}
      >
        {menuItems.map((item) => (
          <MotionListItem
            key={item.text}
            component={RouterLink}
            to={item.path}
            variants={itemVariants}
            sx={{
              borderRadius: 2,
              mb: 0.5,
              color: location.pathname === item.path ? 'primary.main' : 'text.primary',
              bgcolor: location.pathname === item.path
                ? theme.palette.mode === 'light'
                  ? 'rgba(0, 0, 0, 0.04)'
                  : 'rgba(255, 255, 255, 0.05)'
                : 'transparent',
              '&:hover': {
                bgcolor: theme.palette.mode === 'light'
                  ? 'rgba(0, 0, 0, 0.08)'
                  : 'rgba(255, 255, 255, 0.08)',
              },
            }}
          >
            <ListItemIcon
              sx={{
                color: location.pathname === item.path ? 'primary.main' : 'inherit',
                minWidth: 40
              }}
            >
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              primaryTypographyProps={{
                fontWeight: location.pathname === item.path ? 'bold' : 'regular'
              }}
            />
          </MotionListItem>
        ))}
      </MotionList>

      <Divider sx={{ my: 2 }} />

      {/* Profile and settings */}
      <MotionList
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        sx={{ px: 1 }}
      >
        <MotionListItem
          component={RouterLink}
          to="/profile"
          variants={itemVariants}
          sx={{
            borderRadius: 2,
            mb: 0.5,
            color: location.pathname === '/profile' ? 'primary.main' : 'text.primary',
            bgcolor: location.pathname === '/profile'
              ? theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.04)'
                : 'rgba(255, 255, 255, 0.05)'
              : 'transparent',
            '&:hover': {
              bgcolor: theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.08)'
                : 'rgba(255, 255, 255, 0.08)',
            },
          }}
        >
          <ListItemIcon
            sx={{
              color: location.pathname === '/profile' ? 'primary.main' : 'inherit',
              minWidth: 40
            }}
          >
            <Person />
          </ListItemIcon>
          <ListItemText
            primary="Profile"
            primaryTypographyProps={{
              fontWeight: location.pathname === '/profile' ? 'bold' : 'regular'
            }}
          />
        </MotionListItem>

        <MotionListItem
          component={RouterLink}
          to="/settings"
          variants={itemVariants}
          sx={{
            borderRadius: 2,
            color: location.pathname === '/settings' ? 'primary.main' : 'text.primary',
            bgcolor: location.pathname === '/settings'
              ? theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.04)'
                : 'rgba(255, 255, 255, 0.05)'
              : 'transparent',
            '&:hover': {
              bgcolor: theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.08)'
                : 'rgba(255, 255, 255, 0.08)',
            },
          }}
        >
          <ListItemIcon
            sx={{
              color: location.pathname === '/settings' ? 'primary.main' : 'inherit',
              minWidth: 40
            }}
          >
            <Settings />
          </ListItemIcon>
          <ListItemText
            primary="Settings"
            primaryTypographyProps={{
              fontWeight: location.pathname === '/settings' ? 'bold' : 'regular'
            }}
          />
        </MotionListItem>
      </MotionList>
    </Paper>
  );
};

export default Sidebar;
