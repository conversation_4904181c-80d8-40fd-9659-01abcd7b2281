import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Grid,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Delete as DeleteIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getMCQs, deleteMCQ } from '../../api/studentTools';
import { Question } from '../../api/questions';
import { formatDate } from '../../utils/formatters';

interface GeneratedMCQsListProps {
  jobId?: number;
}

const GeneratedMCQsList: React.FC<GeneratedMCQsListProps> = ({ jobId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [mcqToDelete, setMcqToDelete] = useState<Question | null>(null);

  // Fetch MCQs
  const {
    data: mcqs = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['generatedMCQs', jobId],
    queryFn: () => getMCQs(0, 100, jobId),
    staleTime: 30000,
  });

  // Delete MCQ mutation
  const deleteMutation = useMutation({
    mutationFn: deleteMCQ,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generatedMCQs'] });
      setDeleteDialogOpen(false);
      setMcqToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete MCQ:', error);
    }
  });

  // Handle delete button click
  const handleDeleteClick = (mcq: Question) => {
    setMcqToDelete(mcq);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (mcqToDelete) {
      deleteMutation.mutate(mcqToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setMcqToDelete(null);
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load your generated MCQs. Please try again later.
      </Alert>
    );
  }

  if (mcqs.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No MCQs found. Please generate some MCQs first.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" fontWeight="bold">
          Generated MCQs ({mcqs.length})
        </Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={() => refetch()} size="small">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* MCQs Grid */}
      <Grid container spacing={2}>
        {mcqs.map((mcq) => (
          <Grid item xs={12} key={mcq.id}>
            <Card sx={{ borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
              <CardContent sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={mcq.difficulty || 'Medium'}
                      color={getDifficultyColor(mcq.difficulty)}
                      size="small"
                    />
                    <Chip
                      label={`ID: ${mcq.id}`}
                      variant="outlined"
                      size="small"
                    />
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {mcq.created_at ? formatDate(mcq.created_at) : 'Unknown date'}
                  </Typography>
                </Box>

                <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                  {mcq.content.length > 200 
                    ? `${mcq.content.substring(0, 200)}...` 
                    : mcq.content}
                </Typography>

                {mcq.options && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Options:
                    </Typography>
                    {Object.entries(mcq.options).map(([key, value]) => (
                      <Typography 
                        key={key} 
                        variant="body2" 
                        sx={{ 
                          ml: 2, 
                          color: key === mcq.answer ? 'success.main' : 'text.secondary',
                          fontWeight: key === mcq.answer ? 'bold' : 'normal'
                        }}
                      >
                        {key.toUpperCase()}: {value}
                      </Typography>
                    ))}
                  </Box>
                )}
              </CardContent>

              <CardActions sx={{ px: 2, pb: 2, justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<PlayArrowIcon />}
                    onClick={() => navigate(`/mcq/practice/question/${mcq.id}`)}
                  >
                    Practice
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<AssessmentIcon />}
                    onClick={() => navigate(`/questions/${mcq.id}`)}
                  >
                    View Details
                  </Button>
                </Box>

                <Tooltip title="Delete MCQ">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDeleteClick(mcq)}
                    disabled={deleteMutation.isPending}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-mcq-dialog-title"
        aria-describedby="delete-mcq-dialog-description"
      >
        <DialogTitle id="delete-mcq-dialog-title">
          Delete MCQ Question
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-mcq-dialog-description">
            Are you sure you want to delete this MCQ question? This action cannot be undone.
          </DialogContentText>
          {mcqToDelete && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {mcqToDelete.content.length > 100 
                  ? `${mcqToDelete.content.substring(0, 100)}...` 
                  : mcqToDelete.content}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GeneratedMCQsList;
