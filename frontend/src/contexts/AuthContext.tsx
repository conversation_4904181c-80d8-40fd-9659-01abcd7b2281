import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, login as apiLogin, getCurrentUser, LoginRequest } from '../api/auth';
import { getMyTutorProfile } from '../api/tutors';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (data: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  isProfileComplete: boolean;
  isEmailVerified: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tutorProfileExists, setTutorProfileExists] = useState<boolean | null>(null);

  useEffect(() => {
    const initAuth = async () => {
      // Check if we already have a user in localStorage
      const storedUser = localStorage.getItem('user');
      const token = localStorage.getItem('token');

      // If we have a stored user and token, use that instead of the mock user
      if (storedUser && token) {
        try {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          setIsLoading(false);
          return;
        } catch (error) {
          console.error('Failed to parse stored user:', error);
        }
      }

      // Removed automatic mock user login for proper authentication flow

      // Check if token exists (already retrieved above)
      if (token) {
        try {
          // Normal login flow
          const userData = await getCurrentUser();
          setUser(userData);

          // If user is a tutor, check if they have a tutor profile
          if (userData.role === 'tutor') {
            try {
              await getMyTutorProfile();
              setTutorProfileExists(true);
            } catch (error) {
              // 404 means no tutor profile exists
              if (error.response?.status === 404) {
                setTutorProfileExists(false);
              } else {
                console.error('Error checking tutor profile:', error);
                setTutorProfileExists(false);
              }
            }
          }
        } catch (error) {
          console.error('Failed to get current user:', error);
          // Don't remove token for network errors, only for auth errors
          if (error.response && error.response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
          }
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, []);

  const login = async (data: LoginRequest) => {
    const response = await apiLogin(data);
    localStorage.setItem('token', response.access_token);
    const userData = await getCurrentUser();
    // Store user data in localStorage to persist across refreshes
    localStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);

    // If user is a tutor, check if they have a tutor profile
    if (userData.role === 'tutor') {
      try {
        await getMyTutorProfile();
        setTutorProfileExists(true);
      } catch (error) {
        // 404 means no tutor profile exists
        if (error.response?.status === 404) {
          setTutorProfileExists(false);
        } else {
          console.error('Error checking tutor profile:', error);
          setTutorProfileExists(false);
        }
      }
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setTutorProfileExists(null);
  };

  const refreshUser = async () => {
    try {
      // Check if token exists
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No token found during refreshUser');
        throw new Error('Authentication token not found. Please log in again.');
      }

      console.log('Refreshing user data with token:', token.substring(0, 10) + '...');
      const userData = await getCurrentUser();
      console.log('User data refreshed successfully:', userData);

      localStorage.setItem('user', JSON.stringify(userData));
      setUser(userData);
      return userData;
    } catch (error) {
      console.error('Failed to refresh user data:', error);

      // Handle specific error cases
      if (error.response) {
        if (error.response.status === 401) {
          console.error('Authentication token expired or invalid');
          // Clear invalid token and user data
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          setUser(null);
          throw new Error('Your session has expired. Please log in again.');
        } else if (error.response.status === 403) {
          console.error('User does not have permission to access this resource');
          throw new Error('You do not have permission to access this resource.');
        }
      }

      throw error;
    }
  };

  // Check if user has completed their profile
  // For tutors, they need both basic profile AND tutor profile
  // For other users, just basic profile
  const isProfileComplete = user ? (
    user.role === 'tutor'
      ? user.profile_completed && tutorProfileExists === true
      : user.profile_completed
  ) : false;

  // Check if user has verified their email
  const isEmailVerified = user ? user.email_verified : false;

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        refreshUser,
        isProfileComplete,
        isEmailVerified,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
