import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Chip,
  Avatar,
  Rating,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  School,
  People,
  Event,
  Star,
  TrendingUp,
  Add,
  Edit,
  Visibility,
  CalendarToday,
  AttachMoney,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { getMyTutorProfile, getMyTutorDashboard, TutorProfile, TutorDashboardStats } from '../../api/tutors';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, subtitle }) => {
  const theme = useTheme();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        sx={{
          height: '100%',
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
            : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
          border: `1px solid ${theme.palette.divider}`,
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          },
          transition: 'all 0.3s ease-in-out',
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                p: 1,
                borderRadius: 2,
                backgroundColor: color,
                color: 'white',
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Box>
              <Typography variant="h4" component="div" fontWeight="bold">
                {value}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

const TutorDashboard: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch tutor profile
  const {
    data: tutorProfile,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useQuery({
    queryKey: ['tutorProfile'],
    queryFn: getMyTutorProfile,
    retry: false,
  });

  // Fetch dashboard stats
  const {
    data: dashboardStats,
    isLoading: isLoadingStats,
    error: statsError,
  } = useQuery({
    queryKey: ['tutorDashboard'],
    queryFn: getMyTutorDashboard,
    enabled: !!tutorProfile,
  });

  const isLoading = isLoadingProfile || isLoadingStats;

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (profileError && profileError.message.includes('404')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          You haven't set up your tutor profile yet. Create one to start offering tutoring services.
        </Alert>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/complete-tutor-profile')}
          size="large"
        >
          Complete Tutor Profile
        </Button>
      </Box>
    );
  }

  if (profileError || statsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading dashboard: {(profileError || statsError)?.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ maxWidth: '100%' }}>
      {/* Header */}
      <Box
        sx={{
          mb: { xs: 2, sm: 4 },
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobile ? 'flex-start' : 'center',
          gap: 2,
        }}
      >
        <Box>
          <Typography
            variant={isMobile ? 'h5' : 'h4'}
            component="h1"
            fontWeight="bold"
            gutterBottom
          >
            Tutor Dashboard
          </Typography>
          <Typography
            variant={isMobile ? 'body1' : 'h6'}
            color="text.secondary"
            gutterBottom
          >
            Welcome back, {user?.full_name}!
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={() => navigate('/tutor/profile')}
          >
            Edit Profile
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/sessions/new')}
          >
            Create Session
          </Button>
        </Box>
      </Box>

      {/* Profile Summary */}
      {tutorProfile && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              src={tutorProfile.user?.profile_picture_url}
              sx={{ width: 64, height: 64, mr: 2 }}
            >
              {tutorProfile.user?.full_name?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom>
                {tutorProfile.user?.full_name}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <Rating value={tutorProfile.average_rating || 0} readOnly size="small" />
                <Typography variant="body2" color="text.secondary">
                  {tutorProfile.total_reviews} reviews
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {tutorProfile.specializations?.map((course) => (
                  <Chip key={course.id} label={course.name} size="small" />
                ))}
              </Box>
            </Box>
            <Chip
              label={tutorProfile.is_available ? 'Available' : 'Unavailable'}
              color={tutorProfile.is_available ? 'success' : 'default'}
            />
          </Box>
          {tutorProfile.bio && (
            <Typography variant="body2" color="text.secondary">
              {tutorProfile.bio}
            </Typography>
          )}
        </Paper>
      )}

      {/* Statistics Grid */}
      {dashboardStats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Sessions"
              value={dashboardStats.total_sessions}
              icon={<Event sx={{ fontSize: 24 }} />}
              color="#2196f3"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Completed Sessions"
              value={dashboardStats.completed_sessions}
              icon={<School sx={{ fontSize: 24 }} />}
              color="#4caf50"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Upcoming Sessions"
              value={dashboardStats.upcoming_sessions}
              icon={<CalendarToday sx={{ fontSize: 24 }} />}
              color="#ff9800"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Students"
              value={dashboardStats.total_students}
              icon={<People sx={{ fontSize: 24 }} />}
              color="#9c27b0"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Average Rating"
              value={dashboardStats.average_rating?.toFixed(1) || 'N/A'}
              icon={<Star sx={{ fontSize: 24 }} />}
              color="#f44336"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="This Month"
              value={dashboardStats.this_month_sessions}
              icon={<TrendingUp sx={{ fontSize: 24 }} />}
              color="#00bcd4"
              subtitle="Sessions"
            />
          </Grid>

          {dashboardStats.total_earnings && (
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Earnings"
                value={`$${dashboardStats.total_earnings}`}
                icon={<AttachMoney sx={{ fontSize: 24 }} />}
                color="#795548"
              />
            </Grid>
          )}

          {dashboardStats.this_month_earnings && (
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="This Month Earnings"
                value={`$${dashboardStats.this_month_earnings}`}
                icon={<AttachMoney sx={{ fontSize: 24 }} />}
                color="#607d8b"
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Quick Actions */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Add />}
              onClick={() => navigate('/sessions/new')}
            >
              Create Session
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Visibility />}
              onClick={() => navigate('/sessions')}
            >
              View Sessions
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Edit />}
              onClick={() => navigate('/tutor/profile')}
            >
              Edit Profile
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<People />}
              onClick={() => navigate('/tutor/students')}
            >
              My Students
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default TutorDashboard;
