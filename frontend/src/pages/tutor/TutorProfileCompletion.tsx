import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Grid,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  Autocomplete,
  Chip,
  OutlinedInput,
  InputAdornment,
  FormControlLabel,
  Switch,
  Slider,
} from '@mui/material';
import {
  School,
  AttachMoney,
  Person,
  Star,
  CheckCircle,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { getCourses, Course } from '../../api/courses';
import { createMyTutorProfile, TutorProfileCreate } from '../../api/tutors';

// Define steps for the tutor profile completion process
const steps = ['Personal Info', 'Teaching Experience', 'Specializations', 'Rates & Availability', 'Confirmation'];

// Validation schemas for each step
const validationSchemas = [
  // Step 1: Personal Info
  Yup.object({
    bio: Yup.string()
      .min(50, 'Bio must be at least 50 characters')
      .max(1000, 'Bio must be at most 1000 characters')
      .required('Bio is required'),
  }),
  // Step 2: Teaching Experience
  Yup.object({
    experience_years: Yup.number()
      .min(0, 'Experience years must be at least 0')
      .max(50, 'Experience years must be at most 50')
      .required('Experience years is required'),
  }),
  // Step 3: Specializations
  Yup.object({
    specialization_ids: Yup.array()
      .min(1, 'Please select at least one specialization')
      .required('Specializations are required'),
  }),
  // Step 4: Rates & Availability
  Yup.object({
    hourly_rate: Yup.number()
      .min(1, 'Hourly rate must be at least $1')
      .max(1000, 'Hourly rate must be at most $1000')
      .required('Hourly rate is required'),
    preferred_session_type: Yup.string()
      .oneOf(['online', 'in_person', 'both'])
      .required('Session type preference is required'),
    max_students_per_session: Yup.number()
      .min(1, 'Must allow at least 1 student')
      .max(20, 'Cannot exceed 20 students per session')
      .required('Max students per session is required'),
  }),
  // Step 5: Confirmation (no validation needed)
  Yup.object({}),
];

const TutorProfileCompletion: React.FC = () => {
  const navigate = useNavigate();
  const { user, refreshUser } = useAuth();
  const queryClient = useQueryClient();
  const [activeStep, setActiveStep] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [profileComplete, setProfileComplete] = useState(false);

  // Fetch courses for specializations
  const {
    data: courses = [],
    isLoading: isLoadingCourses,
    error: coursesError
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
  });

  // Create tutor profile mutation
  const createProfileMutation = useMutation({
    mutationFn: (data: TutorProfileCreate) => createMyTutorProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tutorProfile'] });
      setProfileComplete(true);
      setTimeout(() => {
        navigate('/tutor/dashboard');
      }, 2000);
    },
    onError: (err: any) => {
      setError(err.response?.data?.detail || 'Failed to create tutor profile. Please try again.');
    },
  });

  const formik = useFormik({
    initialValues: {
      bio: '',
      experience_years: 0,
      hourly_rate: 25,
      is_available: true,
      preferred_session_type: 'both' as 'online' | 'in_person' | 'both',
      max_students_per_session: 1,
      linkedin_url: '',
      website_url: '',
      specialization_ids: [] as number[],
    },
    validationSchema: validationSchemas[activeStep],
    onSubmit: async (values) => {
      if (activeStep < steps.length - 1) {
        handleNext();
        return;
      }

      // On the last step, submit the form
      setError(null);
      createProfileMutation.mutate(values);
    },
  });

  const handleNext = () => {
    const currentSchema = validationSchemas[activeStep];
    try {
      currentSchema.validateSync(formik.values, { abortEarly: false });
      setActiveStep((prevStep) => prevStep + 1);
      setError(null);
    } catch (err: any) {
      // Trigger validation to show errors
      Object.keys(formik.values).forEach(key => {
        formik.setFieldTouched(key, true);
      });
      setError('Please fix the errors before proceeding.');
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Tell us about yourself
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Write a compelling bio that highlights your teaching experience and passion for education.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={6}
                name="bio"
                label="Bio"
                placeholder="I am a passionate educator with expertise in... I enjoy helping students..."
                value={formik.values.bio}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.bio && Boolean(formik.errors.bio)}
                helperText={
                  formik.touched.bio && formik.errors.bio
                    ? formik.errors.bio
                    : `${formik.values.bio.length}/1000 characters`
                }
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Teaching Experience
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Help students understand your level of experience.
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="experience_years"
                label="Years of Teaching Experience"
                value={formik.values.experience_years}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.experience_years && Boolean(formik.errors.experience_years)}
                helperText={formik.touched.experience_years && formik.errors.experience_years}
                inputProps={{ min: 0, max: 50 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="linkedin_url"
                label="LinkedIn Profile (Optional)"
                placeholder="https://linkedin.com/in/yourprofile"
                value={formik.values.linkedin_url}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.linkedin_url && Boolean(formik.errors.linkedin_url)}
                helperText={formik.touched.linkedin_url && formik.errors.linkedin_url}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="website_url"
                label="Personal Website (Optional)"
                placeholder="https://yourwebsite.com"
                value={formik.values.website_url}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.website_url && Boolean(formik.errors.website_url)}
                helperText={formik.touched.website_url && formik.errors.website_url}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Your Specializations
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Select the subjects you're qualified to teach.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth error={formik.touched.specialization_ids && Boolean(formik.errors.specialization_ids)}>
                <InputLabel>Specializations</InputLabel>
                <Select
                  multiple
                  name="specialization_ids"
                  value={formik.values.specialization_ids}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  input={<OutlinedInput label="Specializations" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as number[]).map((value) => {
                        const course = courses.find(c => c.id === value);
                        return (
                          <Chip key={value} label={course?.name || value} size="small" />
                        );
                      })}
                    </Box>
                  )}
                >
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name} ({course.code})
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.specialization_ids && formik.errors.specialization_ids && (
                  <FormHelperText>{formik.errors.specialization_ids}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Rates & Availability
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Set your teaching preferences and rates.
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="hourly_rate"
                label="Hourly Rate"
                value={formik.values.hourly_rate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.hourly_rate && Boolean(formik.errors.hourly_rate)}
                helperText={formik.touched.hourly_rate && formik.errors.hourly_rate}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                inputProps={{ min: 1, max: 1000 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Session Type Preference</InputLabel>
                <Select
                  name="preferred_session_type"
                  value={formik.values.preferred_session_type}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Session Type Preference"
                >
                  <MenuItem value="online">Online Only</MenuItem>
                  <MenuItem value="in_person">In-Person Only</MenuItem>
                  <MenuItem value="both">Both Online & In-Person</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                name="max_students_per_session"
                label="Max Students per Session"
                value={formik.values.max_students_per_session}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.max_students_per_session && Boolean(formik.errors.max_students_per_session)}
                helperText={formik.touched.max_students_per_session && formik.errors.max_students_per_session}
                inputProps={{ min: 1, max: 20 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formik.values.is_available}
                    onChange={(e) => formik.setFieldValue('is_available', e.target.checked)}
                    name="is_available"
                  />
                }
                label="Available for new students"
              />
            </Grid>
          </Grid>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Review Your Profile
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Please review your information before creating your tutor profile.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 3, bgcolor: 'background.default' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Bio:</Typography>
                    <Typography variant="body2" paragraph>
                      {formik.values.bio}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Experience:</Typography>
                    <Typography variant="body2">
                      {formik.values.experience_years} years
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Hourly Rate:</Typography>
                    <Typography variant="body2">
                      ${formik.values.hourly_rate}/hour
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Session Type:</Typography>
                    <Typography variant="body2">
                      {formik.values.preferred_session_type === 'online' ? 'Online Only' :
                       formik.values.preferred_session_type === 'in_person' ? 'In-Person Only' :
                       'Both Online & In-Person'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Max Students:</Typography>
                    <Typography variant="body2">
                      {formik.values.max_students_per_session} per session
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Specializations:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {formik.values.specialization_ids.map((id) => {
                        const course = courses.find(c => c.id === id);
                        return (
                          <Chip key={id} label={course?.name || id} size="small" />
                        );
                      })}
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        );

      default:
        return 'Unknown step';
    }
  };

  if (isLoadingCourses) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <School sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Complete Your Tutor Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Set up your tutoring profile to start connecting with students
          </Typography>
        </Box>

        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Course loading error */}
        {coursesError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            Failed to load courses. Please refresh the page.
          </Alert>
        )}

        {/* Profile completion success message */}
        {profileComplete ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Tutor Profile Created!
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your tutor profile has been successfully created. Redirecting to your dashboard...
            </Typography>
            <CircularProgress size={24} sx={{ mt: 2 }} />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            {/* Step content */}
            {getStepContent(activeStep)}

            {/* Navigation buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                variant="outlined"
              >
                Back
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={createProfileMutation.isLoading}
                startIcon={createProfileMutation.isLoading ? <CircularProgress size={20} /> : null}
              >
                {activeStep === steps.length - 1 ? 'Create Profile' : 'Next'}
              </Button>
            </Box>
          </form>
        )}
      </Paper>
    </Container>
  );
};

export default TutorProfileCompletion;
