import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Container,
  useTheme,
  alpha,
  Stack,
  Chip,
  Avatar,
  Paper,
  Divider
} from '@mui/material';
import {
  School as SchoolIcon,
  Book as BookIcon,
  Quiz as QuizIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  ArrowForward as ArrowForwardIcon,
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Footer from '../components/Layout/Footer';

const Home: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const theme = useTheme();

  const features = [
    {
      title: 'Smart Practice System',
      description: 'Adaptive learning that adjusts to your performance and learning pace.',
      icon: <QuizIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    },
    {
      title: 'Rich Content Library',
      description: 'Comprehensive question banks with multimedia support and detailed explanations.',
      icon: <BookIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    },
    {
      title: 'Performance Analytics',
      description: 'Track your progress with detailed insights and personalized recommendations.',
      icon: <TrendingUpIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    },
  ];

  const benefits = [
    'Personalized learning paths',
    'Real-time progress tracking',
    'Expert tutor support',
    'Mobile-friendly platform',
    'Secure and reliable',
    '24/7 accessibility'
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Medical Student',
      content: 'CampusPQ transformed my study routine. The adaptive practice helped me improve my scores by 40%.',
      rating: 5,
      avatar: 'S'
    },
    {
      name: 'Michael Chen',
      role: 'Engineering Student',
      content: 'The detailed analytics and personalized feedback made all the difference in my exam preparation.',
      rating: 5,
      avatar: 'M'
    },
    {
      name: 'Dr. Emily Davis',
      role: 'Tutor',
      content: 'As an educator, I love how CampusPQ helps me track student progress and customize learning materials.',
      rating: 5,
      avatar: 'E'
    }
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Navigation Bar */}
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          bgcolor: alpha(theme.palette.background.paper, 0.95),
          backdropFilter: 'blur(10px)',
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        }}
      >
        <Container maxWidth="lg">
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              py: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SchoolIcon sx={{ fontSize: 32, color: 'primary.main', mr: 1 }} />
              <Typography
                variant="h5"
                component="div"
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                CampusPQ
              </Typography>
            </Box>
            <Stack direction="row" spacing={2}>
              <Button
                variant="text"
                component={RouterLink}
                to="/admin/login"
                startIcon={<AdminIcon />}
                sx={{
                  borderRadius: 2,
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  }
                }}
              >
                Admin
              </Button>
              {!isAuthenticated ? (
                <>
                  <Button
                    variant="outlined"
                    component={RouterLink}
                    to="/login"
                    sx={{ borderRadius: 2 }}
                  >
                    Login
                  </Button>
                  <Button
                    variant="contained"
                    component={RouterLink}
                    to="/register"
                    sx={{ borderRadius: 2 }}
                  >
                    Get Started
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  component={RouterLink}
                  to="/dashboard"
                  sx={{ borderRadius: 2 }}
                >
                  Go to Dashboard
                </Button>
              )}
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Stack spacing={3}>
                <Chip
                  label="🚀 AI-Powered Learning Platform"
                  sx={{
                    alignSelf: 'flex-start',
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: 'primary.main',
                    fontWeight: 600,
                  }}
                />
                <Typography
                  variant="h2"
                  component="h1"
                  sx={{
                    fontWeight: 800,
                    fontSize: { xs: '2.5rem', md: '3.5rem' },
                    lineHeight: 1.2,
                    background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  Master Your Studies with Smart Learning
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    color: 'text.secondary',
                    fontWeight: 400,
                    lineHeight: 1.6,
                  }}
                >
                  Transform your academic journey with our AI-driven platform that adapts to your learning style and accelerates your success.
                </Typography>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mt: 4 }}>
                  {!isAuthenticated ? (
                    <>
                      <Button
                        variant="contained"
                        size="large"
                        component={RouterLink}
                        to="/register"
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          py: 1.5,
                          px: 4,
                          borderRadius: 3,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          boxShadow: theme.shadows[8],
                        }}
                      >
                        Start Learning Free
                      </Button>
                      <Button
                        variant="outlined"
                        size="large"
                        component={RouterLink}
                        to="/login"
                        sx={{
                          py: 1.5,
                          px: 4,
                          borderRadius: 3,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                        }}
                      >
                        Sign In
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="large"
                      component={RouterLink}
                      to="/dashboard"
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        py: 1.5,
                        px: 4,
                        borderRadius: 3,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                        boxShadow: theme.shadows[8],
                      }}
                    >
                      Continue Learning
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Paper
                  elevation={20}
                  sx={{
                    p: 4,
                    borderRadius: 4,
                    background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  }}
                >
                  <Stack spacing={3} alignItems="center">
                    <SchoolIcon sx={{ fontSize: 80, color: 'primary.main' }} />
                    <Typography variant="h6" align="center" sx={{ fontWeight: 600 }}>
                      Join 10,000+ Students Already Learning Smarter
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <StarIcon key={star} sx={{ color: '#FFD700', fontSize: 24 }} />
                      ))}
                    </Stack>
                    <Typography variant="body2" color="text.secondary" align="center">
                      "The best learning platform I've ever used!"
                    </Typography>
                  </Stack>
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
        <Stack spacing={6}>
          <Box textAlign="center">
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Why Choose CampusPQ?
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto' }}
            >
              Our platform combines cutting-edge AI technology with proven educational methods to deliver personalized learning experiences.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    height: '100%',
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[12],
                      borderColor: alpha(theme.palette.primary.main, 0.3),
                    },
                  }}
                >
                  <Stack spacing={3} alignItems="center" textAlign="center">
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: '50%',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      {feature.icon}
                    </Box>
                    <Typography variant="h5" component="h3" sx={{ fontWeight: 600 }}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.7 }}>
                      {feature.description}
                    </Typography>
                  </Stack>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Stack>
      </Container>

      {/* Benefits Section */}
      <Box sx={{ bgcolor: alpha(theme.palette.primary.main, 0.02), py: { xs: 8, md: 12 } }}>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Stack spacing={3}>
                <Typography
                  variant="h3"
                  component="h2"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '2rem', md: '2.5rem' },
                  }}
                >
                  Everything You Need to Succeed
                </Typography>
                <Typography variant="h6" color="text.secondary" sx={{ lineHeight: 1.7 }}>
                  Our comprehensive platform provides all the tools and resources you need to excel in your academic journey.
                </Typography>
                <Stack spacing={2}>
                  {benefits.map((benefit, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon sx={{ color: 'success.main', mr: 2 }} />
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {benefit}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Paper
                  elevation={8}
                  sx={{
                    p: 6,
                    borderRadius: 4,
                    textAlign: 'center',
                    background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
                  }}
                >
                  <Stack spacing={3} alignItems="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SpeedIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                      <SecurityIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
                      <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main' }} />
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      99.9%
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Platform Uptime
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Reliable, secure, and always available when you need it most.
                    </Typography>
                  </Stack>
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Testimonials Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
        <Stack spacing={6}>
          <Box textAlign="center">
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              What Our Users Say
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto' }}
            >
              Join thousands of students and educators who have transformed their learning experience with CampusPQ.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Paper
                  elevation={2}
                  sx={{
                    p: 4,
                    height: '100%',
                    borderRadius: 3,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: theme.shadows[8],
                    },
                  }}
                >
                  <Stack spacing={3}>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <StarIcon key={i} sx={{ color: '#FFD700', fontSize: 20 }} />
                      ))}
                    </Box>
                    <Typography
                      variant="body1"
                      sx={{
                        fontStyle: 'italic',
                        lineHeight: 1.7,
                        color: 'text.secondary',
                      }}
                    >
                      "{testimonial.content}"
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'primary.main',
                          width: 48,
                          height: 48,
                          fontSize: '1.2rem',
                          fontWeight: 600,
                        }}
                      >
                        {testimonial.avatar}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {testimonial.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {testimonial.role}
                        </Typography>
                      </Box>
                    </Box>
                  </Stack>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Stack>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          py: { xs: 8, md: 12 },
          color: 'white',
        }}
      >
        <Container maxWidth="md">
          <Stack spacing={4} alignItems="center" textAlign="center">
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                fontSize: { xs: '2rem', md: '2.5rem' },
              }}
            >
              Ready to Transform Your Learning?
            </Typography>
            <Typography
              variant="h6"
              sx={{
                opacity: 0.9,
                maxWidth: 500,
                lineHeight: 1.6,
              }}
            >
              Join thousands of students who are already achieving better results with our AI-powered learning platform.
            </Typography>
            {!isAuthenticated && (
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Button
                  variant="contained"
                  size="large"
                  component={RouterLink}
                  to="/register"
                  sx={{
                    py: 1.5,
                    px: 4,
                    borderRadius: 3,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    bgcolor: 'white',
                    color: 'primary.main',
                    '&:hover': {
                      bgcolor: alpha('#ffffff', 0.9),
                    },
                  }}
                >
                  Start Free Today
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  component={RouterLink}
                  to="/login"
                  sx={{
                    py: 1.5,
                    px: 4,
                    borderRadius: 3,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderColor: 'white',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'white',
                      bgcolor: alpha('#ffffff', 0.1),
                    },
                  }}
                >
                  Sign In
                </Button>
              </Stack>
            )}
          </Stack>
        </Container>
      </Box>

      {/* Footer */}
      <Footer />
    </Box>
  );
};

export default Home;
