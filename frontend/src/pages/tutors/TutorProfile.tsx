import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Avatar,
  Rating,
  Chip,
  Button,
  Grid,
  Divider,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Container,
} from '@mui/material';
import {
  LocationOn,
  AttachMoney,
  Language,
  Person,
  School,
  Star,
  ArrowBack,
  Event,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getTutor } from '../../api/tutors';

const TutorProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    data: tutor,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['tutor', id],
    queryFn: () => getTutor(Number(id)),
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !tutor) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          Error loading tutor profile. Please try again.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: { xs: 2, sm: 4 }, px: { xs: 1, sm: 3 } }}>
      {/* Back Button */}
      <Button
        startIcon={<ArrowBack />}
        onClick={() => navigate(-1)}
        sx={{ mb: 3 }}
      >
        Back to Search
      </Button>

      {/* Header Card */}
      <Paper sx={{ p: { xs: 3, sm: 4 }, mb: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, alignItems: { xs: 'center', sm: 'flex-start' }, gap: 3 }}>
          <Avatar
            src={tutor.user?.profile_picture_url}
            sx={{ 
              width: { xs: 100, sm: 120 }, 
              height: { xs: 100, sm: 120 },
              border: '4px solid',
              borderColor: 'primary.light'
            }}
          >
            {tutor.user?.full_name?.charAt(0)}
          </Avatar>
          
          <Box sx={{ flex: 1, textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
              {tutor.user?.full_name}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: { xs: 'center', sm: 'flex-start' }, mb: 2 }}>
              <Rating value={tutor.average_rating || 0} readOnly />
              <Typography variant="body1" sx={{ ml: 1 }}>
                ({tutor.total_reviews} reviews)
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: { xs: 'center', sm: 'flex-start' }, mb: 2 }}>
              <Chip
                icon={<AttachMoney />}
                label={`$${tutor.hourly_rate}/hour`}
                color="primary"
                variant="filled"
              />
              <Chip
                icon={<School />}
                label={`${tutor.experience_years} years experience`}
                variant="outlined"
              />
              <Chip
                label={tutor.is_available ? 'Available' : 'Unavailable'}
                color={tutor.is_available ? 'success' : 'default'}
                variant="filled"
              />
            </Box>

            {/* Additional Info */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, alignItems: { xs: 'center', sm: 'flex-start' } }}>
              {tutor.location && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <LocationOn sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {tutor.location}
                  </Typography>
                </Box>
              )}
              
              {tutor.languages && tutor.languages.length > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Language sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {tutor.languages.join(', ')}
                  </Typography>
                </Box>
              )}

              {tutor.gender && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Person sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {tutor.gender === 'prefer_not_to_say' ? 'Prefer not to say' : 
                     tutor.gender.charAt(0).toUpperCase() + tutor.gender.slice(1)}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ 
          display: 'flex', 
          gap: 2, 
          mt: 3,
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: { xs: 'stretch', sm: 'flex-start' }
        }}>
          <Button
            variant="contained"
            size="large"
            startIcon={<Event />}
            disabled={!tutor.is_available}
            onClick={() => {/* Handle book session */}}
            sx={{ minHeight: '48px' }}
          >
            Book Session
          </Button>
          <Button
            variant="outlined"
            size="large"
            sx={{ minHeight: '48px' }}
          >
            Message Tutor
          </Button>
        </Box>
      </Paper>

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Bio */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              About Me
            </Typography>
            <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
              {tutor.bio || 'No bio available.'}
            </Typography>
          </Paper>
        </Grid>

        {/* Specializations */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Specializations
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {tutor.specializations && tutor.specializations.length > 0 ? (
                tutor.specializations.map((spec) => (
                  <Chip
                    key={spec.id}
                    label={spec.name}
                    variant="outlined"
                    sx={{ mb: 1 }}
                  />
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No specializations listed
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Teaching Details */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Teaching Details
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Session Type
                </Typography>
                <Typography variant="body1">
                  {tutor.preferred_session_type === 'online' ? 'Online Only' :
                   tutor.preferred_session_type === 'in_person' ? 'In-Person Only' :
                   'Online & In-Person'}
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Max Students per Session
                </Typography>
                <Typography variant="body1">
                  {tutor.max_students_per_session} students
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Total Sessions Completed
                </Typography>
                <Typography variant="body1">
                  {tutor.total_sessions_completed} sessions
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Contact Information */}
        {(tutor.linkedin_url || tutor.website_url) && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Contact & Links
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {tutor.linkedin_url && (
                  <Button
                    variant="outlined"
                    href={tutor.linkedin_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    LinkedIn Profile
                  </Button>
                )}
                {tutor.website_url && (
                  <Button
                    variant="outlined"
                    href={tutor.website_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Personal Website
                  </Button>
                )}
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Container>
  );
};

export default TutorProfile;
