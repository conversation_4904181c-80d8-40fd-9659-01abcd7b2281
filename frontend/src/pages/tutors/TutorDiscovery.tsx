import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  Rating,
  Chip,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Search,
  FilterList,
  ExpandMore,
  LocationOn,
  AttachMoney,
  Event,
  Star,
  School,
  Person,
  Language,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { getTutors, searchTutors, TutorProfile, TutorSearchFilters } from '../../api/tutors';
import { getCourses } from '../../api/courses';

const TutorDiscovery: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<TutorSearchFilters>({
    is_available: true,
  });
  const [priceRange, setPriceRange] = useState<number[]>([0, 100]);
  const [showFilters, setShowFilters] = useState(false);

  // Fetch courses for filter options
  const { data: courses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Fetch tutors
  const {
    data: tutors,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['tutors', filters],
    queryFn: () => searchTutors(filters),
  });

  const handleFilterChange = (key: keyof TutorSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handlePriceRangeChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as number[]);
    setFilters(prev => ({
      ...prev,
      max_hourly_rate: (newValue as number[])[1],
    }));
  };

  const TutorCard = ({ tutor }: { tutor: TutorProfile }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flex: 1 }}>
          {/* Tutor Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              src={tutor.user?.profile_picture_url}
              sx={{ width: 60, height: 60, mr: 2 }}
            >
              {tutor.user?.full_name?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom>
                {tutor.user?.full_name}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Rating value={tutor.average_rating || 0} readOnly size="small" />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  ({tutor.total_reviews} reviews)
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AttachMoney sx={{ fontSize: 16, mr: 0.5 }} />
                <Typography variant="body2">
                  ${tutor.hourly_rate}/hour
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Bio */}
          {tutor.bio && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {tutor.bio.length > 150 ? `${tutor.bio.substring(0, 150)}...` : tutor.bio}
            </Typography>
          )}

          {/* Specializations */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Specializations
            </Typography>
            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
              {tutor.specializations?.map((course) => (
                <Chip
                  key={course.id}
                  label={course.name}
                  size="small"
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>

          {/* Stats */}
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {tutor.total_sessions_completed}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Sessions
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {tutor.experience_years || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Years Exp.
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Session Type */}
          {tutor.preferred_session_type && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {tutor.preferred_session_type === 'online' ? 'Online Only' :
                 tutor.preferred_session_type === 'in_person' ? 'In-Person Only' :
                 'Online & In-Person'}
              </Typography>
            </Box>
          )}

          {/* Availability */}
          <Chip
            label={tutor.is_available ? 'Available' : 'Unavailable'}
            color={tutor.is_available ? 'success' : 'default'}
            size="small"
            sx={{ mb: 2 }}
          />
        </CardContent>

        {/* Actions */}
        <Box sx={{ p: 2, pt: 0 }}>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="outlined"
                size="small"
                onClick={() => {/* Handle view profile */}}
              >
                View Profile
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant="contained"
                size="small"
                disabled={!tutor.is_available}
                onClick={() => {/* Handle book session */}}
              >
                Book Session
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Card>
    </motion.div>
  );

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading tutors: {error.message}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Find a Tutor
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Connect with experienced tutors to boost your academic performance
      </Typography>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search by name, subject, or keyword..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Button
              variant="outlined"
              startIcon={<FilterList />}
              onClick={() => setShowFilters(!showFilters)}
              fullWidth
            >
              Filters
            </Button>
          </Grid>
        </Grid>

        {/* Advanced Filters */}
        {showFilters && (
          <Accordion expanded sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>Advanced Filters</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Subject</InputLabel>
                    <Select
                      value={filters.course_ids?.[0] || ''}
                      label="Subject"
                      onChange={(e) => handleFilterChange('course_ids', e.target.value ? [Number(e.target.value)] : undefined)}
                    >
                      <MenuItem value="">All Subjects</MenuItem>
                      {courses?.map((course) => (
                        <MenuItem key={course.id} value={course.id}>
                          {course.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Session Type</InputLabel>
                    <Select
                      value={filters.session_type || ''}
                      label="Session Type"
                      onChange={(e) => handleFilterChange('session_type', e.target.value || undefined)}
                    >
                      <MenuItem value="">Any</MenuItem>
                      <MenuItem value="online">Online</MenuItem>
                      <MenuItem value="in_person">In-Person</MenuItem>
                      <MenuItem value="both">Both</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" gutterBottom>
                    Min Rating
                  </Typography>
                  <Slider
                    value={filters.min_rating || 0}
                    onChange={(e, value) => handleFilterChange('min_rating', value)}
                    min={0}
                    max={5}
                    step={0.5}
                    marks
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" gutterBottom>
                    Price Range ($/hour)
                  </Typography>
                  <Slider
                    value={priceRange}
                    onChange={handlePriceRangeChange}
                    min={0}
                    max={200}
                    step={5}
                    valueLabelDisplay="auto"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        )}
      </Paper>

      {/* Results */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">
          {tutors?.length || 0} tutors found
        </Typography>
      </Box>

      {/* Tutors Grid */}
      <Grid container spacing={3}>
        {tutors?.map((tutor) => (
          <Grid item xs={12} sm={6} md={4} key={tutor.id}>
            <TutorCard tutor={tutor} />
          </Grid>
        ))}
      </Grid>

      {tutors?.length === 0 && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No tutors found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search criteria or filters
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default TutorDiscovery;
