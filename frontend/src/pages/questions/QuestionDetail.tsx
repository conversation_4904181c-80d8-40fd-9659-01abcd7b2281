import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Book as BookIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getQuestion, deleteQuestion, Question as QuestionType } from '../../api/questions';
import { getCourse } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';

const QuestionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const questionId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const canManageQuestion = isAdmin || isTutor;

  // State for practice mode
  const [practiceMode, setPracticeMode] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [answerSubmitted, setAnswerSubmitted] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch question data
  const {
    data: question,
    isLoading: isLoadingQuestion,
    error: questionError
  } = useQuery({
    queryKey: ['question', questionId],
    queryFn: () => getQuestion(questionId),
    enabled: !!questionId
  });

  // Fetch course data
  const {
    data: course,
    isLoading: isLoadingCourse
  } = useQuery({
    queryKey: ['course', question?.course_id],
    queryFn: () => getCourse(question!.course_id),
    enabled: !!question?.course_id
  });

  // Delete question mutation
  const deleteMutation = useMutation({
    mutationFn: () => deleteQuestion(questionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      setDeleteDialogOpen(false);
      setSuccessMessage('Question deleted successfully');
      setTimeout(() => {
        navigate('/questions');
      }, 1500);
    }
  });

  // Handle answer selection
  const handleAnswerChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedAnswer(event.target.value);
  };

  // Handle answer submission
  const handleSubmitAnswer = () => {
    setAnswerSubmitted(true);
  };

  // Handle reset practice
  const handleResetPractice = () => {
    setSelectedAnswer('');
    setAnswerSubmitted(false);
    setShowExplanation(false);
  };

  // Toggle practice mode
  const togglePracticeMode = () => {
    setPracticeMode(!practiceMode);
    handleResetPractice();
  };

  // Check if answer is correct
  const isAnswerCorrect = () => {
    if (!question || !answerSubmitted) return false;
    return selectedAnswer === question.answer;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoadingQuestion) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (questionError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading question: {(questionError as Error).message}
      </Alert>
    );
  }

  if (!question) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Question not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/questions')}
          sx={{ mr: 2 }}
        >
          Back to Questions
        </Button>

        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          Question
        </Typography>

        {canManageQuestion && (
          <>
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              component={RouterLink}
              to={`/questions/${question.id}/edit`}
              sx={{ mr: 1 }}
            >
              Edit
            </Button>

            {(isAdmin || (isTutor && question.created_by_id === user?.id)) && (
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={() => setDeleteDialogOpen(true)}
              >
                Delete
              </Button>
            )}
          </>
        )}
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip
                  label={question.question_type.replace('_', ' ')}
                  color="primary"
                />
                <Chip
                  label={question.difficulty}
                  color={getDifficultyColor(question.difficulty)}
                />
              </Box>

              <Button
                variant="outlined"
                size="small"
                startIcon={practiceMode ? <VisibilityIcon /> : <VisibilityOffIcon />}
                onClick={togglePracticeMode}
              >
                {practiceMode ? 'View Mode' : 'Practice Mode'}
              </Button>
            </Box>

            <Typography variant="h6" gutterBottom>
              Question:
            </Typography>
            <Typography variant="body1" paragraph>
              {question.content}
            </Typography>

            {question.media_url && (
              <Box sx={{ my: 2, textAlign: 'center' }}>
                <img
                  src={question.media_url}
                  alt="Question media"
                  style={{ maxWidth: '100%', maxHeight: '300px' }}
                />
              </Box>
            )}

            <Divider sx={{ my: 3 }} />

            {question.question_type === 'multiple_choice' && question.options && (
              <Box sx={{ mb: 3 }}>
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend">Options:</FormLabel>
                  <RadioGroup
                    value={selectedAnswer}
                    onChange={handleAnswerChange}
                  >
                    {Object.entries(question.options).map(([key, value]) => (
                      <FormControlLabel
                        key={key}
                        value={key}
                        control={<Radio />}
                        label={value}
                        disabled={practiceMode && answerSubmitted}
                        sx={{
                          ...(practiceMode && answerSubmitted && {
                            color: key === question.answer ? 'success.main' :
                                  (key === selectedAnswer ? 'error.main' : 'inherit')
                          })
                        }}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>

                {practiceMode && (
                  <Box sx={{ mt: 2 }}>
                    {!answerSubmitted ? (
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSubmitAnswer}
                        disabled={!selectedAnswer}
                      >
                        Submit Answer
                      </Button>
                    ) : (
                      <Box>
                        {isAnswerCorrect() ? (
                          <Alert severity="success" sx={{ mb: 2 }}>
                            Correct! Well done.
                          </Alert>
                        ) : (
                          <Alert severity="error" sx={{ mb: 2 }}>
                            Incorrect. The correct answer is {question.answer}.
                          </Alert>
                        )}

                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Button
                            variant="outlined"
                            onClick={() => setShowExplanation(!showExplanation)}
                          >
                            {showExplanation ? 'Hide Explanation' : 'Show Explanation'}
                          </Button>

                          <Button
                            variant="contained"
                            onClick={handleResetPractice}
                          >
                            Try Again
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            )}

            {question.question_type === 'flashcard' && (
              <Box sx={{ mb: 3 }}>
                {practiceMode ? (
                  <Box>
                    {!answerSubmitted ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleSubmitAnswer}
                          size="large"
                        >
                          Show Answer
                        </Button>
                      </Box>
                    ) : (
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          Answer:
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {question.answer}
                        </Typography>

                        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                          <Button
                            variant="outlined"
                            onClick={() => setShowExplanation(!showExplanation)}
                          >
                            {showExplanation ? 'Hide Explanation' : 'Show Explanation'}
                          </Button>

                          <Button
                            variant="contained"
                            onClick={handleResetPractice}
                          >
                            Reset
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Answer:
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {question.answer}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {question.question_type === 'open_ended' && (
              <Box sx={{ mb: 3 }}>
                {practiceMode ? (
                  <Box>
                    {!answerSubmitted ? (
                      <Box>
                        <TextField
                          label="Your Answer"
                          multiline
                          rows={4}
                          fullWidth
                          value={selectedAnswer}
                          onChange={(e) => setSelectedAnswer(e.target.value)}
                          sx={{ mb: 2 }}
                        />
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleSubmitAnswer}
                          disabled={!selectedAnswer}
                        >
                          Submit Answer
                        </Button>
                      </Box>
                    ) : (
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          Your Answer:
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {selectedAnswer}
                        </Typography>

                        <Typography variant="h6" gutterBottom>
                          Sample Answer:
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {question.answer}
                        </Typography>

                        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                          <Button
                            variant="outlined"
                            onClick={() => setShowExplanation(!showExplanation)}
                          >
                            {showExplanation ? 'Hide Explanation' : 'Show Explanation'}
                          </Button>

                          <Button
                            variant="contained"
                            onClick={handleResetPractice}
                          >
                            Try Again
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Sample Answer:
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {question.answer}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {(!practiceMode || showExplanation) && question.explanation && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Explanation:
                </Typography>
                <Typography variant="body1">
                  {question.explanation}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Question Information
            </Typography>

            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Course
              </Typography>
              <Typography variant="body1">
                {isLoadingCourse ? (
                  <CircularProgress size={16} />
                ) : course ? (
                  <Button
                    component={RouterLink}
                    to={`/courses/${course.id}`}
                    size="small"
                    sx={{ p: 0, minWidth: 0, textTransform: 'none' }}
                  >
                    {course.name}
                  </Button>
                ) : (
                  'Unknown Course'
                )}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Type
              </Typography>
              <Chip
                label={question.question_type.replace('_', ' ')}
                color="primary"
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Difficulty
              </Typography>
              <Chip
                label={question.difficulty}
                color={getDifficultyColor(question.difficulty)}
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>

            {question.topic && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Topic
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {question.topic}
                </Typography>
              </Box>
            )}

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Chip
                label={question.is_active ? 'Active' : 'Inactive'}
                color={question.is_active ? 'success' : 'default'}
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
              <Typography variant="body2">
                {formatDate(question.created_at)}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body2">
                {formatDate(question.updated_at)}
              </Typography>
            </Box>
          </Paper>

          {canManageQuestion && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Actions
              </Typography>

              <Button
                fullWidth
                variant="outlined"
                color="primary"
                component={RouterLink}
                to={`/questions/${question.id}/edit`}
                sx={{ mb: 2 }}
              >
                Edit Question
              </Button>

              {(isAdmin || (isTutor && question.created_by_id === user?.id)) && (
                <Button
                  fullWidth
                  variant="outlined"
                  color="error"
                  onClick={() => setDeleteDialogOpen(true)}
                >
                  Delete Question
                </Button>
              )}
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Question</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this question? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => deleteMutation.mutate()}
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default QuestionDetail;
