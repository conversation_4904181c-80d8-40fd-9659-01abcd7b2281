import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  useTheme,
  useMediaQuery,
  Container,
  Paper,
  Breadcrumbs,
  Link,
  alpha
} from '@mui/material';
import {
  Quiz as QuizIcon,
  Summarize as SummarizeIcon,
  ViewCarousel as CardIcon,
  CloudUpload as CloudUploadIcon,
  Home as HomeIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';

// Tool configurations
interface Tool {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  route: string;
  color: 'primary' | 'secondary' | 'info' | 'warning';
}

const ToolsDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5">Please log in to access tools</Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/login')}
          sx={{ mt: 2 }}
        >
          Log In
        </Button>
      </Box>
    );
  }

  // Tool configurations
  const tools: Tool[] = [
    {
      id: 'mcq',
      title: 'MCQ Generator',
      description: 'Generate multiple-choice questions from your notes',
      icon: QuizIcon,
      color: 'primary',
      route: '/tools/mcq'
    },
    {
      id: 'flashcards',
      title: 'Flash Card Generator',
      description: 'Create interactive flash cards for active recall',
      icon: CardIcon,
      color: 'secondary',
      route: '/flash-cards'
    },
    {
      id: 'summaries',
      title: 'AI Summary Generator',
      description: 'Create comprehensive summaries using AI',
      icon: SummarizeIcon,
      color: 'info',
      route: '/summaries'
    }
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Breadcrumbs */}
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ pt: 2, pb: 1 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              color="inherit"
              href="/dashboard"
              sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <AutoAwesomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Study Tools
            </Typography>
          </Breadcrumbs>
        </Box>
      </Container>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ py: { xs: 2, md: 4 } }}>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 3, md: 4 }, px: { xs: 1, sm: 0 } }}>
            <Typography
              variant={isMobile ? "h4" : "h3"}
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.75rem', sm: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              Study Tools
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                maxWidth: '600px'
              }}
            >
              Choose a tool to enhance your learning experience
            </Typography>
          </Box>

          {/* PDF Upload Section */}
          <Paper
            sx={{
              borderRadius: 2,
              bgcolor: 'background.paper',
              border: 1,
              borderColor: 'divider',
              overflow: 'hidden',
              mb: { xs: 4, md: 6 }
            }}
          >
            <Box
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                p: { xs: 3, sm: 4 },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 2, sm: 3 },
                textAlign: { xs: 'center', sm: 'left' }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: { xs: 48, sm: 56 },
                    height: { xs: 48, sm: 56 },
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.primary.main, 0.2),
                    border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`
                  }}
                >
                  <CloudUploadIcon
                    sx={{
                      fontSize: { xs: 24, sm: 28 },
                      color: 'primary.main'
                    }}
                  />
                </Box>
                <Box>
                  <Typography
                    variant="h6"
                    fontWeight="600"
                    sx={{
                      fontSize: { xs: '1.1rem', sm: '1.25rem' },
                      color: 'text.primary',
                      mb: 0.5
                    }}
                  >
                    Upload Study Materials
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontSize: { xs: '0.875rem', sm: '0.875rem' } }}
                  >
                    Upload PDF notes to use with the tools below
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate('/tools/upload')}
                sx={{
                  borderRadius: 2,
                  px: { xs: 3, sm: 4 },
                  py: { xs: 1, sm: 1.25 },
                  fontSize: { xs: '0.875rem', sm: '1rem' },
                  fontWeight: 600,
                  textTransform: 'none',
                  minWidth: { xs: '100%', sm: 'auto' }
                }}
              >
                Upload PDFs
              </Button>
            </Box>
          </Paper>

          {/* Tools Grid */}
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            {tools.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <Grid item xs={12} sm={6} md={4} key={tool.id}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'all 0.3s ease-in-out',
                      cursor: 'pointer',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: theme.shadows[8],
                        '& .tool-icon': {
                          transform: 'scale(1.05)'
                        }
                      },
                      borderRadius: 2,
                      bgcolor: 'background.paper',
                      border: 1,
                      borderColor: 'divider'
                    }}
                    onClick={() => navigate(tool.route)}
                  >
                    <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 }, textAlign: 'center' }}>
                      <Box
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: { xs: 56, sm: 64 },
                          height: { xs: 56, sm: 64 },
                          borderRadius: '50%',
                          bgcolor: alpha(theme.palette[tool.color].main, 0.1),
                          mb: { xs: 2, sm: 3 },
                          border: `2px solid ${alpha(theme.palette[tool.color].main, 0.2)}`
                        }}
                      >
                        <IconComponent
                          className="tool-icon"
                          sx={{
                            fontSize: { xs: 28, sm: 32 },
                            color: `${tool.color}.main`,
                            transition: 'all 0.3s ease'
                          }}
                        />
                      </Box>

                      <Typography
                        variant="h6"
                        fontWeight="600"
                        gutterBottom
                        sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}
                      >
                        {tool.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          fontSize: { xs: '0.875rem', sm: '0.875rem' },
                          lineHeight: 1.5
                        }}
                      >
                        {tool.description}
                      </Typography>

                      <Button
                        variant="contained"
                        color={tool.color}
                        fullWidth
                        sx={{
                          borderRadius: 2,
                          py: { xs: 1, sm: 1.25 },
                          fontSize: { xs: '0.875rem', sm: '1rem' },
                          fontWeight: 600,
                          textTransform: 'none'
                        }}
                      >
                        Open Tool
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>

        </Box>
      </Container>
    </Box>
  );
};

export default ToolsDashboard;
