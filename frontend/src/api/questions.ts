import apiClient from './client';

export type QuestionType = 'multiple_choice' | 'flashcard' | 'open_ended';
export type DifficultyLevel = 'easy' | 'medium' | 'hard';

export interface Question {
  id: number;
  content: string;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer: string;
  explanation?: string;
  media_url?: string;
  is_active: boolean;
  course_id: number;
  created_by_id: number;
  created_at: string;
  updated_at: string;
}

export interface QuestionCreate {
  content: string;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer: string;
  explanation?: string;
  media_url?: string;
  is_active?: boolean;
  course_id: number;
}

export interface QuestionUpdate {
  content?: string;
  question_type?: QuestionType;
  difficulty?: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer?: string;
  explanation?: string;
  media_url?: string;
  is_active?: boolean;
  course_id?: number;
}

export interface AIQuestionRequest {
  course_id: number;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic: string;
  num_questions?: number;
}

export const getQuestions = async (
  courseId?: number,
  difficulty?: DifficultyLevel,
  questionType?: QuestionType,
  topic?: string
): Promise<Question[]> => {
  const params: Record<string, any> = {};
  if (courseId) params.course_id = courseId;
  if (difficulty) params.difficulty = difficulty;
  if (questionType) params.question_type = questionType;
  if (topic) params.topic = topic;

  try {
    console.log('Fetching questions with params:', params);
    const response = await apiClient.get<Question[]>('/questions/', { params });
    console.log('Questions from API:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw error;
  }
};

export const getQuestion = async (id: number): Promise<Question> => {
  const response = await apiClient.get<Question>(`/questions/${id}`);
  return response.data;
};

export const createQuestion = async (data: QuestionCreate): Promise<Question> => {
  const response = await apiClient.post<Question>('/questions', data);
  return response.data;
};

export const updateQuestion = async (id: number, data: QuestionUpdate): Promise<Question> => {
  const response = await apiClient.put<Question>(`/questions/${id}`, data);
  return response.data;
};

export const deleteQuestion = async (id: number): Promise<Question> => {
  const response = await apiClient.delete<Question>(`/questions/${id}`);
  return response.data;
};

export const generateAIQuestions = async (data: AIQuestionRequest): Promise<Question[]> => {
  const response = await apiClient.post<Question[]>('/ai/generate-questions', data);
  return response.data;
};

export const uploadPdfForQuestions = async (
  file: File,
  courseId: number,
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  numQuestions: number = 5
): Promise<Question[]> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('course_id', courseId.toString());
  formData.append('question_type', questionType);
  formData.append('difficulty', difficulty);
  formData.append('num_questions', numQuestions.toString());

  const response = await apiClient.post<Question[]>('/questions/upload-pdf', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
